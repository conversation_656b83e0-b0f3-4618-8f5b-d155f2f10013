<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Pond Report - Tomota Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        aqua: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            background-color: #fafafa;
            color: #333;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .sidebar, .tomota-sidebar {
            width: 230px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: none;
            position: fixed;
            left: 0 !important;
            top: 0;
            margin-left: 0 !important;
            z-index: 1200;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            font-family: "Helvetica", "Roboto", Arial, sans-serif;
            transition: transform 0.3s ease;
        }

        .tomota-sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-logo {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f5f5f5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 77px;
        }

        .logo {
            width: 218px;
            height: 77px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 4px;
            padding: 8px;
        }

        .logo-image {
            width: 160px;
            height: 100px;
            object-fit: contain;
            background-color: transparent;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }

        .sidebar-nav {
            flex: 1;
            padding: 8px 0;
            overflow-y: auto;
            background-color: #f5f5f5;
        }

        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-item:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .nav-item.active {
            background-color: rgba(0, 0, 0, 0.08);
            color: #303030;
            font-weight: 500;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 400;
        }

        /* Header Styles */
        .header, .header-section {
            height: 110px;
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            color: #fff;
            box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
            position: fixed;
            top: 0;
            left: 230px;
            right: 0;
            z-index: 9999;
            transition: left 0.3s ease;
        }

        .header-section.sidebar-hidden {
            left: 0;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 0 24px;
            min-height: 90px;
        }

        .toolbar-grid {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .left-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .menu-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .menu-button svg {
            width: 32px;
            height: 32px;
            fill: currentColor;
        }

        .farm-info {
            color: black;
        }

        .header-icon-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .header-icon-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .header-icon-button img {
            width: 24px;
            height: 24px;
        }

        .avatar-button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            background-color: #1976d2;
        }

        .avatar-button:hover {
            background-color: rgba(25, 118, 210, 0.8);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Main Content Area */
        .main-content {
            margin-left: 230px;
            margin-top: 110px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        .content-container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* Breadcrumbs */
        .breadcrumbs {
            padding: 16px 24px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .breadcrumbs a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            text-decoration: underline;
        }

        /* Content Header */
        .content-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-header h2 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #333;
        }

        /* Controls */
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .farm-selector {
            display: flex;
            flex-direction: column;
        }

        .farm-selector label {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #555;
        }

        .farm-selector select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            min-width: 200px;
        }

        .date-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-controls input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            text-align: center;
        }

        .date-controls button {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .date-controls button:hover {
            background: #f8f9fa;
        }

        /* Tabs */
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            padding: 0 24px;
            background: #f8f9fa;
        }

        .content-tab {
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            margin-bottom: -1px;
            text-decoration: none;
            color: inherit;
        }

        .content-tab.active {
            color: #ff6b00;
            border-bottom-color: #ff6b00;
        }

        .content-tab:not(.active):hover {
            color: #007bff;
        }

        /* Legend */
        .legend {
            display: flex;
            gap: 24px;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            display: inline-block;
            margin-right: 8px;
        }

        /* Status Colors - Match pond colors exactly */
        .active-pond { 
            background-color: #219653 !important;
        }
        .stocking-pond { 
            background-color: #3ac1ff !important;
        }
        .renovation-pond { 
            background-color: #03256b !important;
        }
        .risk-pond-1 { 
            background-color: #ce262d !important;
        }
        .risk-pond-2 { 
            background-color: #ffcc00 !important;
        }
        .risk-pond-3 { 
            background-color: #ffff00 !important;
        }

        /* Table */
        .table-container {
            overflow-x: auto;
            padding: 0 24px 24px;
            width: 100%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        th, td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .pond-cell {
            background: #c7d6e3;
            min-width: 40px;
        }

        .pond-cell.active {
            background: white;
            border: 1px solid #000;
        }

        .pond-number {
            font-size: 12px;
        }

        /* Tab Content Styling */
        .tab-content {
            display: block;
        }

        .tab-content.hidden {
            display: none;
        }

        /* Additional styles for better table appearance */
        .table-container table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        .table-container th, .table-container td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        .table-container thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        /* Additional styles for AquaPond Analytics */
        .header-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0d9488 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .status-active {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
        }
        .status-stocking {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            color: white;
        }
        .status-renovation {
            background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
            color: white;
        }
        .status-risk {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
            color: white;
        }
        .table-header {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .hover-row:hover {
            background-color: #f0f9ff;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
        .progress-bg {
            background-color: #e2e8f0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #0ea5e9, #0d9488);
        }
        .summary-card {
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(14, 165, 233, 0); }
            100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body>
    <!-- Tomota Sidebar -->
    <div class="tomota-sidebar">
        <!-- Logo Section -->
        <div class="sidebar-logo">
            <div class="logo-container">
                <div class="logo">
                    <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/Ferris%20wheel.svg" alt="Ferris Wheel Logo" class="logo-image">
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-home-page-64.png" alt="Dashboard" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Dashboard</span>
            </button>

            <button class="nav-item active" onclick="setActive(this)">
                <div class="nav-icon">
                    <svg style="width: 34px; height: 30px; fill: #666;" viewBox="0 0 24 24">
                        <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Active pond report</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-ranking-100%20(1).png" alt="Ponds ranking" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Ponds ranking</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-stir-48.png" alt="Food mixing center" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Food mixing center</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-worker-67.png" alt="Work management" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Work management</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/location.png" alt="Farm System" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Farm System</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/—Pngtree—indian%20rupee%20note%20icon%20png_6668571.png" alt="Payment" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Payment</span>
            </button>

            <button class="nav-item" onclick="setActive(this)">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/Settings%20wheel.svg" alt="Setting" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Setting</span>
            </button>
        </nav>
    </div>

    <!-- Header Section -->
    <div class="header-section">
        <div class="toolbar">
            <div class="toolbar-grid">
                <div class="left-section">
                    <button class="menu-button" onclick="toggleSidebar()">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="farm-info">
                        <div style="font-size: 12px; opacity: 0.8;">Current Farm</div>
                        <div style="font-size: 16px; font-weight: 500;">Sikindar Shaik</div>
                    </div>
                </div>
                <div class="right-section">
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/bell-svgrepo-com.svg" alt="Bell" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/Ferris wheel.svg" alt="Ferris Wheel" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/icons8-alert-48.png" alt="Alert" style="height: 24px; width: 24px;">
                    </button>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Flag_of_India.svg" alt="Flag of India" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">
                    <button class="avatar-button">
                        <div class="user-avatar" style="background: #1976d2; color: white; display: flex; align-items: center; justify-content: center; font-weight: bold;">D</div>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
        <div class="content-container">
            <!-- Breadcrumbs -->
            <div class="breadcrumbs">
                <nav aria-label="breadcrumb">
                    <a href="/en">Home</a> › Active pond report
                </nav>
            </div>

            <!-- Content Header -->
            <div class="content-header">
                <h2>Active pond report</h2>
                <div class="controls">
                    <div class="farm-selector">
                        <label for="farm-select">Farm</label>
                        <select id="farm-select">
                            <option value="Sikindar Shaik">Sikindar Shaik</option>
                        </select>
                    </div>
                    <div class="date-controls">
                        <button>
                            <svg viewBox="0 0 24 24" style="width: 20px; height: 20px; fill: #555;">
                                <path d="M14 7l-5 5 5 5V7z"/>
                            </svg>
                        </button>
                        <input type="text" value="28/07/2025" readonly>
                        <button>
                            <svg viewBox="0 0 24 24" style="width: 20px; height: 20px; fill: #555;">
                                <path d="M10 17l5-5-5-5v10z"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="content-tabs">
                <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
                <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
            </div>

            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color active-pond"></div>
                    <span>Active pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color stocking-pond"></div>
                    <span>Stocking pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color renovation-pond"></div>
                    <span>Renovation pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color risk-pond-1"></div>
                    <div class="legend-color risk-pond-2"></div>
                    <div class="legend-color risk-pond-3"></div>
                    <a href="/en/risk-management" target="_blank">Risk pond</a>
                </div>
            </div>




            <!-- Tab Content: Active Pond Overview -->
            <div id="active-pond-content" class="tab-content">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">AquaPond Analytics</h1>
                                <p class="text-gray-700 mt-2">Real-time monitoring and management of aquaculture ponds</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-sync-alt mr-2 text-primary-600"></i>
                                    <span>Last updated: July 29, 2025, 1:54 PM IST</span>
                                </div>
                                <button class="bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-download mr-2"></i> Export Report
                                </button>
                            </div>
                        </div>
                        <!-- Breadcrumbs -->
                        <nav class="flex text-sm mb-6" aria-label="Breadcrumb">
                            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                <li class="inline-flex items-center">
                                    <a href="#" class="inline-flex items-center text-gray-600 hover:text-primary-600">
                                        <i class="fas fa-home mr-2"></i>
                                        Home
                                    </a>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="#" class="ml-1 text-gray-600 hover:text-primary-600 md:ml-2">Reports</a>
                                    </div>
                                </li>
                                <li aria-current="page">
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="ml-1 text-gray-800 md:ml-2 font-medium">Active Ponds</span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <!-- Filters Bar -->
                    <div class="mb-8 bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label for="farmSelect" class="block text-sm font-medium text-gray-700 mb-2">Farm Location</label>
                                <select id="farmSelect" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>Sikindar Shaik Farm</option>
                                    <option>AquaPrime Holdings</option>
                                    <option>Coastal Fisheries</option>
                                    <option>Oceanic Aquaculture</option>
                                </select>
                            </div>
                            <div>
                                <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <input type="date" id="date" value="2025-07-29" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                            </div>
                            <div>
                                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
                                <select id="statusFilter" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>All Statuses</option>
                                    <option>Active</option>
                                    <option>Stocking</option>
                                    <option>Renovation</option>
                                    <option>At Risk</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button class="w-full bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white font-semibold px-4 py-3 rounded-xl transition duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-filter mr-2"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Active Card -->
                        <div class="bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Active Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">8</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>12% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Stocking Card -->
                        <div class="bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Stocking Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">4</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>3% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Renovation Card -->
                        <div class="bg-gradient-to-br from-purple-400 to-fuchsia-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Renovation Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">2</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tools text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-equals mr-1"></i>
                                    <span>No change from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- At Risk Card -->
                        <div class="bg-gradient-to-br from-red-400 to-orange-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Ponds At Risk</p>
                                    <p class="text-4xl font-bold text-white mt-1">1</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>50% from last month</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Main Content - Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Pond Management Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed information on all aquaculture ponds</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search ponds..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-cog mr-1"></i> Settings
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond #</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Shrimp Type</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stocking Date</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Age (Days)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">ABW (g)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Survival %</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Biomass (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Feed/Day (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">FCR</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Row 1 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 1</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">44</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">28.4 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 89%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">89%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,250 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.2 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.25</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 2</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">39</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">26.7 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 92%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">92%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,180 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">42.8 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.22</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 3</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Stocking
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-07-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.0 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">0.8 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-gray-300" style="width: 0%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">-</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">5</span> of <span class="font-bold">8</span> results
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-primary-600 to-aqua-600 rounded-xl hover:from-primary-700 hover:to-aqua-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-primary-50 to-aqua-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-primary-100 to-aqua-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl text-primary-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Real-time pond performance analytics</p>
                                    <p class="text-sm text-gray-500 mt-2">Growth rates, feed efficiency, and yield predictions</p>
                                </div>
                            </div>
                        </div>
                        <!-- Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-primary-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-plus text-primary-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Add New Pond</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-aqua-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-sync-alt text-aqua-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Refresh Data</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-file-export text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Export Report</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-bell text-green-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Set Alerts</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">89.2%</p>
                                <p class="text-sm text-gray-500">Across active ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.24</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Growth Rate</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">0.29</p>
                                <p class="text-sm text-gray-500">g/day average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-tachometer-alt text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Efficiency</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">92.5%</p>
                                <p class="text-sm text-gray-500">Overall performance</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Content: Farm Report -->
            <div id="farm-report-content" class="tab-content" style="display: none;">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">Farm Management Dashboard</h1>
                                <p class="text-gray-700 mt-2">Comprehensive farm operations and production analytics</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-calendar-alt mr-2 text-emerald-600"></i>
                                    <span>Season: 2025 Monsoon</span>
                                </div>
                                <button class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-file-excel mr-2"></i> Export Data
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Overview Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Ponds Card -->
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">15</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-chart-line mr-1"></i>
                                    <span>Across 3 modules</span>
                                </div>
                            </div>
                        </div>
                        <!-- Total Area Card -->
                        <div class="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Area</p>
                                    <p class="text-4xl font-bold text-white mt-1">45.2</p>
                                    <p class="text-xs text-white opacity-80">Hectares</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-expand-arrows-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>5% expansion planned</span>
                                </div>
                            </div>
                        </div>
                        <!-- Production Capacity Card -->
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Production</p>
                                    <p class="text-4xl font-bold text-white mt-1">125</p>
                                    <p class="text-xs text-white opacity-80">Tons/Season</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-target mr-1"></i>
                                    <span>Target: 150 tons</span>
                                </div>
                            </div>
                        </div>
                        <!-- Efficiency Card -->
                        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Efficiency</p>
                                    <p class="text-4xl font-bold text-white mt-1">83%</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>8% from last season</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Production Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100 mb-8">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Farm Production Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed production data across all farm modules</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search modules..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-filter mr-1"></i> Filter
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond Count</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Total Area (Ha)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Season</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stock Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">PL Origin</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Expected Yield</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Current Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Progress</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Module A Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">5 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.2 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Coastal Hatchery</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.6 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 75%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">75%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module B Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">6 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">18.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Premium Aqua</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">52.3 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 68%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">68%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module C Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">4 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-yellow-100 text-yellow-800">
                                                Preparation
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">13.2 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Marine Genetics</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">38.1 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Preparing
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-yellow-500" style="width: 25%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">25%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">3</span> of <span class="font-bold">3</span> modules
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-emerald-600 to-teal-600 rounded-xl hover:from-emerald-700 hover:to-teal-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar text-4xl text-emerald-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Production analytics chart would appear here</p>
                                    <p class="text-sm text-gray-500 mt-2">Showing yield trends across all modules</p>
                                </div>
                            </div>
                        </div>
                        <!-- Farm Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-emerald-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-seedling text-emerald-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Plan Stocking</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-teal-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-calendar-check text-teal-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Schedule Harvest</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">View Reports</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-cog text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Farm Settings</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">87.5%</p>
                                <p class="text-sm text-gray-500">Average across all ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.28</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-clock text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Cycle Time</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">105</p>
                                <p class="text-sm text-gray-500">Days average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-dollar-sign text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Profit Margin</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">24.3%</p>
                                <p class="text-sm text-gray-500">This season</p>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <script>
        function setActive(element) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function toggleSidebar() {
            const sidebar = document.querySelector('.tomota-sidebar');
            const mainContent = document.querySelector('.main-content');
            const header = document.querySelector('.header-section');
            
            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
            header.classList.toggle('sidebar-hidden');
        }

        function switchTab(tabName, element) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.content-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Add active class to clicked tab
            element.classList.add('active');
        }
    </script>
</body>
</html>
