<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Active Pond Report - Tomota Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        aqua: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        /* Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            background-color: #fafafa;
            color: #333;
            line-height: 1.5;
        }

        /* Sidebar Styles */
        .sidebar, .tomota-sidebar {
            width: 230px;
            height: 100vh;
            background-color: #f5f5f5;
            border-right: none;
            position: fixed;
            left: 0 !important;
            top: 0;
            margin-left: 0 !important;
            z-index: 1200;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            font-family: "Helvetica", "Roboto", Arial, sans-serif;
            transition: transform 0.3s ease;
        }

        .tomota-sidebar.hidden {
            transform: translateX(-100%);
        }

        .sidebar-logo {
            padding: 16px;
            text-align: center;
            border-bottom: 1px solid #e0e0e0;
            background-color: #f5f5f5;
        }

        .logo-container {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 77px;
        }

        .logo {
            width: 218px;
            height: 77px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: transparent;
            border-radius: 4px;
            padding: 8px;
        }

        .logo-image {
            width: 160px;
            height: 100px;
            object-fit: contain;
            background-color: transparent;
        }

        .logo-text {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }

        .sidebar-nav {
            flex: 1;
            padding: 8px 0;
            overflow-y: auto;
            background-color: #f5f5f5;
        }

        .nav-item {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px 16px;
            border: none;
            background: none;
            cursor: pointer;
            transition: background-color 0.2s ease;
            color: #565656;
            font-size: 14px;
            font-weight: 400;
            text-align: left;
            font-family: inherit;
        }

        .nav-item:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .nav-item.active {
            background-color: rgba(0, 0, 0, 0.08);
            color: #303030;
            font-weight: 500;
        }

        .nav-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            flex: 1;
            font-size: 14px;
            font-weight: 400;
        }

        /* Header Styles */
        .header, .header-section {
            height: 110px;
            background: linear-gradient(90deg, rgba(0, 148, 255, 0.3) 0%, rgba(223, 216, 219, 0.3) 50%, rgba(255, 203, 191, 0.3) 100%);
            color: #fff;
            box-shadow: 0px 2px 4px -1px rgba(0,0,0,0.2), 0px 4px 5px 0px rgba(0,0,0,0.14), 0px 1px 10px 0px rgba(0,0,0,0.12);
            position: fixed;
            top: 0;
            left: 230px;
            right: 0;
            z-index: 9999;
            transition: left 0.3s ease;
        }

        .header-section.sidebar-hidden {
            left: 0;
        }

        .toolbar {
            display: flex;
            align-items: center;
            padding: 0 24px;
            min-height: 90px;
        }

        .toolbar-grid {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 100%;
        }

        .left-section {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .right-section {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .menu-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 16px;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .menu-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .menu-button svg {
            width: 32px;
            height: 32px;
            fill: currentColor;
        }

        .farm-info {
            color: black;
        }

        .header-icon-button {
            background: none;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: inherit;
        }

        .header-icon-button:hover {
            background-color: rgba(255, 255, 255, 0.08);
        }

        .header-icon-button img {
            width: 24px;
            height: 24px;
        }

        .avatar-button {
            background: none;
            border: none;
            padding: 6px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            background-color: #1976d2;
        }

        .avatar-button:hover {
            background-color: rgba(25, 118, 210, 0.8);
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
        }

        /* Main Content Area */
        .main-content {
            margin-left: 230px;
            margin-top: 110px;
            padding: 20px;
            transition: margin-left 0.3s ease;
        }

        .main-content.sidebar-hidden {
            margin-left: 0;
        }

        .content-container {
            max-width: 100%;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            overflow: hidden;
        }

        /* Breadcrumbs */
        .breadcrumbs {
            padding: 16px 24px 0;
            font-size: 14px;
            color: #6c757d;
        }

        .breadcrumbs a {
            color: #007bff;
            text-decoration: none;
        }

        .breadcrumbs a:hover {
            text-decoration: underline;
        }

        /* Content Header */
        .content-header {
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-header h2 {
            font-size: 24px;
            font-weight: 500;
            margin-bottom: 16px;
            color: #333;
        }

        /* Controls */
        .controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
        }

        .farm-selector {
            display: flex;
            flex-direction: column;
        }

        .farm-selector label {
            font-size: 12px;
            font-weight: 500;
            margin-bottom: 4px;
            color: #555;
        }

        .farm-selector select {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            min-width: 200px;
        }

        .date-controls {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .date-controls input {
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
            width: 120px;
            text-align: center;
        }

        .date-controls button {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            width: 40px;
            height: 40px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .date-controls button:hover {
            background: #f8f9fa;
        }

        /* Tabs */
        .content-tabs {
            display: flex;
            border-bottom: 1px solid #e0e0e0;
            padding: 0 24px;
            background: #f8f9fa;
        }

        .content-tab {
            padding: 12px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            margin-bottom: -1px;
            text-decoration: none;
            color: inherit;
        }

        .content-tab.active {
            color: #ff6b00;
            border-bottom-color: #ff6b00;
        }

        .content-tab:not(.active):hover {
            color: #007bff;
        }

        /* Legend */
        .legend {
            display: flex;
            gap: 24px;
            padding: 16px 24px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .legend-color {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            border: 2px solid #fff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            display: inline-block;
            margin-right: 8px;
        }

        /* Status Colors - Match pond colors exactly */
        .active-pond {
            background-color: #219653 !important;
        }
        .stocking-pond {
            background-color: #3ac1ff !important;
        }
        .renovation-pond {
            background-color: #03256b !important;
        }
        .risk-pond-1 {
            background-color: #ce262d !important;
        }
        .risk-pond-2 {
            background-color: #ffcc00 !important;
        }
        .risk-pond-3 {
            background-color: #ffff00 !important;
        }

        /* Table */
        .table-container {
            overflow-x: auto;
            padding: 0 24px 24px;
            width: 100%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        th, td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        .pond-cell {
            background: #c7d6e3;
            min-width: 40px;
        }

        .pond-cell.active {
            background: white;
            border: 1px solid #000;
        }

        .pond-number {
            font-size: 12px;
        }

        /* Tab Content Styling */
        .tab-content {
            display: block;
        }

        .tab-content.hidden {
            display: none;
        }

        /* Additional styles for better table appearance */
        .table-container table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1600px;
        }

        .table-container th, .table-container td {
            padding: 10px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            font-size: 13px;
            white-space: nowrap;
            min-width: 40px;
        }

        .table-container thead th {
            background: #f1f3f5;
            font-weight: 500;
            color: #495057;
            position: sticky;
            top: 0;
        }

        /* Additional styles for AquaPond Analytics */
        .header-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0d9488 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .status-active {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
        }
        .status-stocking {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            color: white;
        }
        .status-renovation {
            background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
            color: white;
        }
        .status-risk {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
            color: white;
        }
        .table-header {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .hover-row:hover {
            background-color: #f0f9ff;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
        .progress-bg {
            background-color: #e2e8f0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #0ea5e9, #0d9488);
        }
        .summary-card {
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(14, 165, 233, 0); }
            100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        /* User Dropdown Menu Styles */
        .user-dropdown {
            position: relative;
            display: inline-block;
        }

        .dropdown-menu {
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
            min-width: 280px;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            margin-top: 8px;
        }

        .dropdown-menu.show {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .dropdown-header {
            padding: 20px;
            border-bottom: 1px solid #f0f0f0;
            text-align: center;
        }

        .dropdown-header h3 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: #333;
        }

        .dropdown-header p {
            margin: 5px 0 0 0;
            font-size: 14px;
            color: #666;
        }

        .dropdown-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f5f5f5;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s ease;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        .dropdown-item:last-child {
            border-bottom: none;
        }

        .dropdown-item-label {
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .dropdown-item-value {
            font-size: 14px;
            color: #666;
        }

        .dropdown-action {
            padding: 15px 20px;
            cursor: pointer;
            transition: background-color 0.2s ease;
            font-size: 14px;
            color: #333;
            font-weight: 500;
        }

        .dropdown-action:hover {
            background-color: #f8f9fa;
        }

        .dropdown-action.logout {
            color: #dc3545;
            border-top: 1px solid #f0f0f0;
        }

        .dropdown-action.logout:hover {
            background-color: #fdf2f2;
        }

        /* Modern Avatar Styles */
        .user-avatar-modern {
            position: relative;
            width: 44px;
            height: 44px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .user-avatar-modern:hover {
            transform: scale(1.05);
        }

        .avatar-inner {
            position: relative;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            overflow: hidden;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }

        .avatar-letter {
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            z-index: 3;
            position: relative;
        }

        .avatar-ring {
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            border: 2px solid transparent;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            animation: gradientShift 3s ease infinite;
            z-index: 1;
        }

        .avatar-glow {
            position: absolute;
            top: 50%;
            left: 50%;
            width: 60px;
            height: 60px;
            background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: pulse 2s ease-in-out infinite;
            z-index: 0;
        }

        .avatar-status {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 14px;
            height: 14px;
            background: linear-gradient(135deg, #4ade80, #22c55e);
            border: 2px solid white;
            border-radius: 50%;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            z-index: 4;
        }

        .avatar-status::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 6px;
            height: 6px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            animation: statusPulse 1.5s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes statusPulse {
            0%, 100% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
            50% { opacity: 0.7; transform: translate(-50%, -50%) scale(0.8); }
        }

        /* Avatar button hover effects */
        .avatar-button {
            background: none;
            border: none;
            padding: 0;
            cursor: pointer;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .avatar-button:hover .avatar-inner {
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .avatar-button:hover .avatar-glow {
            animation: pulse 1s ease-in-out infinite;
        }

        .avatar-button:active .user-avatar-modern {
            transform: scale(0.95);
        }

        /* Dashboard Styles */
        .dashboard-section {
            background: #f8f9fa;
            min-height: calc(100vh - 120px);
            padding: 0;
        }

        .dashboard-header {
            background: white;
            padding: 16px 24px;
            border-bottom: 1px solid #e0e0e0;
        }

        .farm-selector-section {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .farm-label {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            min-width: 40px;
        }

        .farm-dropdown {
            position: relative;
        }

        .farm-select {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #333;
            min-width: 200px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .farm-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .dashboard-main {
            padding: 24px;
        }

        .breadcrumb-section {
            margin-bottom: 16px;
        }

        .breadcrumb-nav {
            font-size: 14px;
            color: #666;
        }

        .breadcrumb-item {
            color: #666;
        }

        .title-and-filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 32px;
            flex-wrap: wrap;
            gap: 16px;
        }

        .dashboard-title {
            font-size: 32px;
            font-weight: 400;
            color: #333;
            margin: 0;
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
        }

        .filter-controls {
            display: flex;
            gap: 16px;
            align-items: center;
        }

        .filter-group {
            position: relative;
        }

        .filter-select {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 8px 32px 8px 12px;
            font-size: 14px;
            color: #666;
            min-width: 120px;
            cursor: pointer;
            appearance: none;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 8px center;
            background-repeat: no-repeat;
            background-size: 16px;
        }

        .filter-select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
        }

        .filter-select:hover {
            border-color: #bbb;
        }

        /* Map container adjustments for dashboard */
        .dashboard-section .map-container {
            margin: 0 24px 24px 24px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .title-and-filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-controls {
                width: 100%;
                flex-wrap: wrap;
            }

            .filter-group {
                flex: 1;
                min-width: 100px;
            }

            .filter-select {
                width: 100%;
            }
        }

        .MuiBox-root {
            box-sizing: border-box;
        }

        .jss73 {
            padding: 24px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .jss74 {
            margin-bottom: 24px;
        }

        .jss75 {
            margin-bottom: 16px;
        }

        .MuiBreadcrumbs-root {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-size: 0.875rem;
            line-height: 1.43;
            letter-spacing: 0.01071em;
            color: rgba(0, 0, 0, 0.6);
        }

        .MuiBreadcrumbs-ol {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            padding: 0;
            margin: 0;
            list-style: none;
        }

        .MuiBreadcrumbs-li {
            display: flex;
            align-items: center;
        }

        .jss76 {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.87);
        }

        .jss78 {
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .jss79 {
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            font-size: 2.125rem;
            line-height: 1.235;
            letter-spacing: 0.00735em;
            color: rgba(0, 0, 0, 0.87);
            margin: 0;
        }

        .jss80 {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .jss81 {
            width: 100%;
        }

        .MuiContainer-root {
            width: 100%;
            margin-left: auto;
            margin-right: auto;
            padding-left: 16px;
            padding-right: 16px;
        }

        .MuiContainer-maxWidthLg {
            max-width: 1280px;
        }

        .jss84 {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .jss85 {
            display: flex;
            gap: 16px;
            align-items: center;
            flex-wrap: wrap;
        }

        .jss86, .jss93, .jss95 {
            min-width: 200px;
            flex: 1;
        }

        .MuiAutocomplete-root {
            position: relative;
            display: inline-flex;
            vertical-align: top;
        }

        .MuiFormControl-root {
            display: inline-flex;
            flex-direction: column;
            position: relative;
            min-width: 0;
            padding: 0;
            margin: 0;
            border: 0;
            vertical-align: top;
        }

        .MuiFormControl-fullWidth {
            width: 100%;
        }

        .MuiInputLabel-root {
            color: rgba(0, 0, 0, 0.54);
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            font-size: 1rem;
            line-height: 1;
            letter-spacing: 0.00938em;
            padding: 0;
            position: absolute;
            left: 0;
            top: 0;
            transform: translate(14px, 16px) scale(1);
            transition: color 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms,transform 200ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
            z-index: 1;
            pointer-events: none;
        }

        .MuiInputLabel-outlined {
            transform: translate(14px, 20px) scale(1);
        }

        .MuiInputLabel-marginDense {
            transform: translate(14px, 12px) scale(1);
        }

        .MuiInputBase-root {
            color: rgba(0, 0, 0, 0.87);
            cursor: text;
            display: inline-flex;
            position: relative;
            font-size: 1rem;
            box-sizing: border-box;
            align-items: center;
            font-family: "Roboto", "Helvetica", "Arial", sans-serif;
            font-weight: 400;
            line-height: 1.1876em;
            letter-spacing: 0.00938em;
        }

        .MuiOutlinedInput-root {
            position: relative;
            border-radius: 4px;
        }

        .MuiInputBase-fullWidth {
            width: 100%;
        }

        .MuiInputBase-marginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiInputBase-input {
            font: inherit;
            letter-spacing: inherit;
            color: currentColor;
            padding: 18.5px 14px;
            border: 0;
            box-sizing: content-box;
            background: none;
            height: 1.1876em;
            margin: 0;
            display: block;
            min-width: 0;
            width: 100%;
            animation-name: mui-auto-fill-cancel;
            animation-duration: 10ms;
        }

        .MuiOutlinedInput-input {
            padding: 18.5px 14px;
        }

        .MuiInputBase-inputMarginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiOutlinedInput-inputMarginDense {
            padding-top: 12px;
            padding-bottom: 12px;
        }

        .MuiInputBase-inputAdornedEnd {
            padding-right: 0;
        }

        .MuiOutlinedInput-inputAdornedEnd {
            padding-right: 0;
        }

        .MuiAutocomplete-endAdornment {
            position: absolute;
            right: 9px;
            top: calc(50% - 12px);
        }

        .MuiIconButton-root {
            flex: 0 0 auto;
            color: rgba(0, 0, 0, 0.54);
            padding: 12px;
            overflow: visible;
            font-size: 1.5rem;
            text-align: center;
            transition: background-color 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            border-radius: 50%;
            background-color: transparent;
            border: none;
            cursor: pointer;
        }

        .MuiIconButton-root:hover {
            background-color: rgba(0, 0, 0, 0.04);
        }

        .MuiSvgIcon-root {
            fill: currentColor;
            width: 1em;
            height: 1em;
            display: inline-block;
            font-size: 1.5rem;
            transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
            flex-shrink: 0;
            user-select: none;
        }

        .MuiSvgIcon-fontSizeSmall {
            font-size: 1.25rem;
        }

        .MuiOutlinedInput-notchedOutline {
            border-color: rgba(0, 0, 0, 0.23);
            border-style: solid;
            border-width: 1px;
            border-radius: inherit;
            position: absolute;
            top: -5px;
            left: 0;
            right: 0;
            bottom: 0;
            margin: 0;
            padding: 0 8px;
            pointer-events: none;
        }

        .jss89 {
            border-color: rgba(0, 0, 0, 0.23);
        }

        .jss91 {
            width: auto;
            height: 11px;
            display: block;
            padding: 0;
            font-size: 0.75em;
            max-width: 0.01px;
            text-align: left;
            transition: max-width 50ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;
            visibility: hidden;
        }

        .jss91 span {
            padding-left: 5px;
            padding-right: 5px;
            display: inline-block;
        }

        .jss97 {
            margin-top: 32px;
        }

        .jss98 {
            height: 200px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .jss98::before {
            content: "Dashboard Content Area";
            color: #666;
            font-size: 18px;
            font-weight: 500;
        }

        /* Map Section Styles */
        .map-container {
            margin-top: 32px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .map-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        }

        .map-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin: 0;
        }

        .map-controls {
            display: flex;
            gap: 8px;
        }

        .map-control-btn {
            padding: 8px 16px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #666;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .map-control-btn:hover {
            background: #f0f0f0;
            border-color: #bbb;
        }

        .map-control-btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .map-wrapper {
            display: flex;
            height: 600px;
        }

        .farm-map {
            flex: 1;
            position: relative;
            background: #f0f8ff;
        }

        .map-placeholder {
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 40px;
            text-align: center;
        }

        .map-loading {
            max-width: 400px;
        }

        .map-features {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin: 24px 0;
            width: 100%;
        }

        .feature-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 8px;
            font-size: 14px;
            color: #555;
        }

        .feature-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .feature-icon.active-pond {
            background: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
        }

        .feature-icon.stocking-pond {
            background: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
        }

        .feature-icon.renovation-pond {
            background: #6f42c1;
            box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.3);
        }

        .feature-icon.risk-pond {
            background: #dc3545;
            box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
        }

        .load-map-btn {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }

        .load-map-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
        }

        .map-sidebar {
            width: 320px;
            background: #f8f9fa;
            border-left: 1px solid #e0e0e0;
            overflow-y: auto;
        }

        .pond-list {
            padding: 20px;
        }

        .pond-list-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin: 0 0 16px 0;
        }

        .pond-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px;
            background: white;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #e0e0e0;
        }

        .pond-item:hover {
            background: #f0f8ff;
            border-color: #007bff;
            transform: translateX(4px);
        }

        .pond-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            flex-shrink: 0;
        }

        .pond-status.active {
            background: #28a745;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
        }

        .pond-status.stocking {
            background: #007bff;
            box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.3);
        }

        .pond-status.renovation {
            background: #6f42c1;
            box-shadow: 0 0 0 2px rgba(111, 66, 193, 0.3);
        }

        .pond-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .pond-name {
            font-weight: 600;
            color: #333;
            font-size: 14px;
        }

        .pond-details {
            font-size: 12px;
            color: #666;
        }

        .pond-item i {
            color: #999;
            font-size: 12px;
        }

        /* Satellite Map Styles */
        .satellite-map-container {
            margin: 24px 0;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            overflow: hidden;
            position: relative;
            width: 100% !important;
            max-width: none !important;
            height: 800px !important;
        }

        .map-view-controls {
            position: absolute;
            top: 16px;
            left: 16px;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .view-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }

        .view-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: #64748b;
            font-size: 0.875rem;
            font-weight: 500;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .view-btn.active {
            background: #3b82f6;
            color: white;
            box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
        }

        .fullscreen-btn {
            position: absolute;
            top: 16px;
            right: 16px;
            z-index: 10;
            padding: 10px;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 8px;
            color: #64748b;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        .fullscreen-btn:hover {
            background: white;
            color: #3b82f6;
        }

        .satellite-map-wrapper {
            height: 800px !important;
            position: relative;
            min-height: 700px;
            width: 100%;
        }

        .satellite-map-view {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        .satellite-background {
            width: 100%;
            height: 100%;
            position: relative;
            background: linear-gradient(135deg, #2d5a3d 0%, #1a3d2e 50%, #0f2419 100%);
        }

        .location-marker {
            z-index: 5;
        }

        .marker-pin {
            width: 24px;
            height: 24px;
            background: #ef4444;
            border-radius: 50% 50% 50% 0;
            transform: rotate(-45deg);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(239, 68, 68, 0.4);
        }

        .marker-pin i {
            transform: rotate(45deg);
            color: white;
            font-size: 12px;
        }

        .marker-label {
            position: absolute;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .location-marker:hover .marker-label {
            opacity: 1;
        }

        .pond-marker {
            z-index: 4;
            cursor: pointer;
        }

        .pond-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
            transition: all 0.2s ease;
        }

        .active-marker .pond-dot {
            background: #10b981;
        }

        .stocking-marker .pond-dot {
            background: #3b82f6;
        }

        .renovation-marker .pond-dot {
            background: #f59e0b;
        }

        .risk-marker .pond-dot {
            background: #ef4444;
        }

        .pond-marker:hover .pond-dot {
            transform: scale(1.2);
        }

        .pond-tooltip {
            position: absolute;
            top: -35px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 6px 10px;
            border-radius: 6px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            transition: opacity 0.2s ease;
            pointer-events: none;
        }

        .pond-marker:hover .pond-tooltip {
            opacity: 1;
        }

        .map-zoom-controls {
            position: absolute;
            bottom: 20px;
            right: 20px;
            z-index: 10;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .zoom-btn {
            width: 40px;
            height: 40px;
            background: rgba(255, 255, 255, 0.95);
            border: none;
            border-radius: 6px;
            color: #64748b;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            transition: all 0.2s ease;
        }

        .zoom-btn:hover {
            background: white;
            color: #3b82f6;
            transform: scale(1.05);
        }

        /* Responsive design for map */
        @media (max-width: 768px) {
            .map-wrapper {
                flex-direction: column;
                height: auto;
            }

            .farm-map {
                height: 400px;
            }

            .map-sidebar {
                width: 100%;
                max-height: 300px;
            }

            .map-header {
                flex-direction: column;
                gap: 16px;
                align-items: flex-start;
            }

            .map-features {
                grid-template-columns: 1fr;
            }

            .satellite-map-wrapper {
                height: 600px !important;
                min-height: 500px;
            }

            .satellite-map-container {
                height: 600px !important;
            }

            .satellite-map-container {
                margin: 16px 0;
                border-radius: 12px;
            }

            .map-view-controls {
                position: absolute;
                top: 12px;
                left: 12px;
                margin: 0;
            }

            .view-toggle {
                padding: 2px;
            }

            .view-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }

            .fullscreen-btn {
                top: 12px;
                right: 12px;
                padding: 8px;
            }

            .zoom-btn {
                width: 36px;
                height: 36px;
            }

            .map-zoom-controls {
                bottom: 16px;
                right: 16px;
            }
        }

        /* AquaPond Analytics Styles */
        .aquapond-filters {
            background-color: #f8fafc;
        }

        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        .autocomplete-container {
            position: relative;
        }

        .autocomplete-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 0.875rem;
            transition: all 0.2s;
            background-color: white;
        }

        .autocomplete-input:focus {
            outline: none;
            border-color: #0ea5e9;
            box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
        }

        .autocomplete-label {
            display: block;
            font-size: 0.75rem;
            font-weight: 500;
            color: #6b7280;
            margin-bottom: 4px;
        }

        /* Tailwind-like utility classes for the filters */
        .max-w-7xl {
            max-width: 80rem;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .sm\\:px-6 {
            padding-left: 1.5rem;
            padding-right: 1.5rem;
        }

        .lg\\:px-8 {
            padding-left: 2rem;
            padding-right: 2rem;
        }

        .py-8 {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .mb-8 {
            margin-bottom: 2rem;
        }

        .bg-white {
            background-color: white;
        }

        .rounded-xl {
            border-radius: 0.75rem;
        }

        .p-6 {
            padding: 1.5rem;
        }

        .grid {
            display: grid;
        }

        .grid-cols-1 {
            grid-template-columns: repeat(1, minmax(0, 1fr));
        }

        .gap-4 {
            gap: 1rem;
        }

        .relative {
            position: relative;
        }

        .absolute {
            position: absolute;
        }

        .inset-y-0 {
            top: 0;
            bottom: 0;
        }

        .right-0 {
            right: 0;
        }

        .flex {
            display: flex;
        }

        .items-center {
            align-items: center;
        }

        .pr-3 {
            padding-right: 0.75rem;
        }

        .text-gray-400 {
            color: #9ca3af;
        }

        /* Responsive grid for medium screens and up */
        @media (min-width: 768px) {
            .md\\:grid-cols-3 {
                grid-template-columns: repeat(3, minmax(0, 1fr));
            }
        }

        /* Responsive padding for small screens and up */
        @media (min-width: 640px) {
            .sm\\:px-6 {
                padding-left: 1.5rem;
                padding-right: 1.5rem;
            }
        }

        /* Responsive padding for large screens and up */
        @media (min-width: 1024px) {
            .lg\\:px-8 {
                padding-left: 2rem;
                padding-right: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Tomota Sidebar -->
    <div class="tomota-sidebar">
        <!-- Logo Section -->
        <div class="sidebar-logo">
            <div class="logo-container">
                <div class="logo">
                    <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/Ferris%20wheel.svg" alt="Ferris Wheel Logo" class="logo-image">
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="sidebar-nav">
            <button class="nav-item" onclick="setActive(this); showDashboard();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-home-page-64.png" alt="Dashboard" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Dashboard</span>
            </button>

            <button class="nav-item active" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <svg style="width: 34px; height: 30px; fill: #666;" viewBox="0 0 24 24">
                        <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"/>
                    </svg>
                </div>
                <span class="nav-text">Active pond report</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-ranking-100%20(1).png" alt="Ponds ranking" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Ponds ranking</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-stir-48.png" alt="Food mixing center" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Food mixing center</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/icons8-worker-67.png" alt="Work management" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Work management</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/location.png" alt="Farm System" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Farm System</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/—Pngtree—indian%20rupee%20note%20icon%20png_6668571.png" alt="Payment" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Payment</span>
            </button>

            <button class="nav-item" onclick="setActive(this); showTabs();">
                <div class="nav-icon">
                    <img src="file:///C:/Users/<USER>/Downloads/Settings%20wheel.svg" alt="Setting" style="width: 34px; height: 30px; object-fit: contain; display: block;" />
                </div>
                <span class="nav-text">Setting</span>
            </button>
        </nav>
    </div>

    <!-- Header Section -->
    <div class="header-section">
        <div class="toolbar">
            <div class="toolbar-grid">
                <div class="left-section">
                    <button class="menu-button" onclick="toggleSidebar()">
                        <svg viewBox="0 0 24 24">
                            <path d="M3 18h18v-2H3v2zm0-5h18v-2H3v2zm0-7v2h18V6H3z"/>
                        </svg>
                    </button>
                    <div class="farm-info">
                        <div style="font-size: 12px; opacity: 0.8;">Current Farm</div>
                        <div style="font-size: 16px; font-weight: 500;">Sikindar Shaik</div>
                    </div>
                </div>
                <div class="right-section">
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Desktop/New%20folder%20(4)/bell-svgrepo-com.svg" alt="Bell" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/Ferris wheel.svg" alt="Ferris Wheel" style="height: 34px; width: 30px;">
                    </button>
                    <button class="header-icon-button">
                        <img src="file:///C:/Users/<USER>/Downloads/icons8-alert-48.png" alt="Alert" style="height: 24px; width: 24px;">
                    </button>
                    <img src="https://upload.wikimedia.org/wikipedia/commons/4/41/Flag_of_India.svg" alt="Flag of India" style="width: 32px; height: 32px; border-radius: 4px; object-fit: cover;">
                    <div class="user-dropdown">
                        <button class="avatar-button" onclick="toggleUserDropdown()">
                            <div class="user-avatar-modern">
                                <div class="avatar-inner">
                                    <span class="avatar-letter">D</span>
                                    <div class="avatar-ring"></div>
                                    <div class="avatar-glow"></div>
                                </div>
                                <div class="avatar-status"></div>
                            </div>
                        </button>
                        <div class="dropdown-menu" id="userDropdown">
                            <div class="dropdown-header">
                                <h3>Sikindar Shaik</h3>
                                <p><EMAIL></p>
                            </div>
                            <div class="dropdown-item">
                                <span class="dropdown-item-label">Profile Information</span>
                                <span class="dropdown-item-value">No information found</span>
                            </div>
                            <div class="dropdown-item">
                                <span class="dropdown-item-label">Contact Details</span>
                                <span class="dropdown-item-value">No information found</span>
                            </div>
                            <div class="dropdown-item">
                                <span class="dropdown-item-label">Currency unit</span>
                                <span class="dropdown-item-value">VND</span>
                            </div>
                            <div class="dropdown-action">
                                Account management
                            </div>
                            <div class="dropdown-action">
                                Management invitation
                            </div>
                            <div class="dropdown-action logout" onclick="logout()">
                                Logout
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="main-content">
        <div class="content-container">




            <!-- Dashboard Content -->
            <div id="dashboard-content" class="dashboard-section" style="display: none;">
                <!-- AquaPond Analytics Filters -->
                <div class="aquapond-filters">
                    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                        <!-- Filters Bar -->
                        <div class="mb-8 bg-white rounded-xl card-shadow p-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <!-- Farm Filter -->
                                <div>
                                    <div class="autocomplete-container">
                                        <label class="autocomplete-label">Farm</label>
                                        <div class="relative">
                                            <input type="text" class="autocomplete-input" placeholder="Select Farm" value="Sikindar Shaik Farm">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <i class="fas fa-chevron-down text-gray-400"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Pond Filter -->
                                <div>
                                    <div class="autocomplete-container">
                                        <label class="autocomplete-label">Pond</label>
                                        <div class="relative">
                                            <input type="text" class="autocomplete-input" placeholder="Select Pond">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <i class="fas fa-chevron-down text-gray-400"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Season Filter -->
                                <div>
                                    <div class="autocomplete-container">
                                        <label class="autocomplete-label">Season</label>
                                        <div class="relative">
                                            <input type="text" class="autocomplete-input" placeholder="Select Season">
                                            <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                                                <i class="fas fa-chevron-down text-gray-400"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>






                <!-- Satellite Map Section -->
                <div class="satellite-map-container">
                    <div class="map-view-controls">
                        <div class="view-toggle">
                            <button class="view-btn active" onclick="switchView('map')">Map</button>
                            <button class="view-btn" onclick="switchView('satellite')">Satellite</button>
                        </div>
                        <button class="fullscreen-btn" onclick="toggleFullscreen()">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                    <div class="satellite-map-wrapper">
                        <div id="satelliteMap" class="satellite-map-view">
                            <!-- Satellite Map Background -->
                            <div class="satellite-background">
                                <img src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 600'%3E%3Cdefs%3E%3Cpattern id='satellite' patternUnits='userSpaceOnUse' width='100' height='100'%3E%3Crect width='100' height='100' fill='%23234a3a'/%3E%3Cpath d='M20 20h60v60H20z' fill='%23345a4a'/%3E%3Cpath d='M40 40h20v20H40z' fill='%23456a5a'/%3E%3C/pattern%3E%3C/defs%3E%3Crect width='1200' height='600' fill='url(%23satellite)'/%3E%3C/svg%3E" alt="Satellite View" style="width: 100%; height: 100%; object-fit: cover;">

                                <!-- Farm Location Marker -->
                                <div class="location-marker" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                                    <div class="marker-pin">
                                        <i class="fas fa-map-marker-alt"></i>
                                    </div>
                                    <div class="marker-label">Sikindar Shaik Farm</div>
                                </div>

                                <!-- Pond Markers - Better distributed across the map -->
                                <div class="pond-marker active-marker" style="position: absolute; top: 25%; left: 30%;" onclick="showPondInfo('A1')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond A1 - Active</div>
                                </div>

                                <div class="pond-marker active-marker" style="position: absolute; top: 35%; left: 65%;" onclick="showPondInfo('A2')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond A2 - Active</div>
                                </div>

                                <div class="pond-marker stocking-marker" style="position: absolute; top: 65%; left: 75%;" onclick="showPondInfo('B1')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond B1 - Stocking</div>
                                </div>

                                <div class="pond-marker renovation-marker" style="position: absolute; top: 75%; left: 35%;" onclick="showPondInfo('B2')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond B2 - Renovation</div>
                                </div>

                                <div class="pond-marker active-marker" style="position: absolute; top: 45%; left: 20%;" onclick="showPondInfo('C1')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond C1 - Active</div>
                                </div>

                                <!-- Additional pond markers for more realistic farm layout -->
                                <div class="pond-marker active-marker" style="position: absolute; top: 60%; left: 55%;" onclick="showPondInfo('D1')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond D1 - Active</div>
                                </div>

                                <div class="pond-marker active-marker" style="position: absolute; top: 30%; left: 85%;" onclick="showPondInfo('E1')">
                                    <div class="pond-dot"></div>
                                    <div class="pond-tooltip">Pond E1 - Active</div>
                                </div>

                                <!-- Map Controls -->
                                <div class="map-zoom-controls">
                                    <button class="zoom-btn" onclick="zoomIn()">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button class="zoom-btn" onclick="zoomOut()">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tabs -->
            <div class="content-tabs">
                <button class="content-tab active" onclick="switchTab('active-pond', this)">Active Pond Overview</button>
                <button class="content-tab" onclick="switchTab('farm-report', this)">Farm Report</button>
            </div>

            <!-- Legend -->
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color active-pond"></div>
                    <span>Active pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color stocking-pond"></div>
                    <span>Stocking pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color renovation-pond"></div>
                    <span>Renovation pond</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color risk-pond-1"></div>
                    <div class="legend-color risk-pond-2"></div>
                    <div class="legend-color risk-pond-3"></div>
                    <a href="/en/risk-management" target="_blank">Risk pond</a>
                </div>
            </div>




            <!-- Tab Content: Active Pond Overview -->
            <div id="active-pond-content" class="tab-content">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">AquaPond Analytics</h1>
                                <p class="text-gray-700 mt-2">Real-time monitoring and management of aquaculture ponds</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-sync-alt mr-2 text-primary-600"></i>
                                    <span>Last updated: July 29, 2025, 1:54 PM IST</span>
                                </div>
                                <button class="bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-download mr-2"></i> Export Report
                                </button>
                            </div>
                        </div>
                        <!-- Breadcrumbs -->
                        <nav class="flex text-sm mb-6" aria-label="Breadcrumb">
                            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                                <li class="inline-flex items-center">
                                    <a href="#" class="inline-flex items-center text-gray-600 hover:text-primary-600">
                                        <i class="fas fa-home mr-2"></i>
                                        Home
                                    </a>
                                </li>
                                <li>
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <a href="#" class="ml-1 text-gray-600 hover:text-primary-600 md:ml-2">Reports</a>
                                    </div>
                                </li>
                                <li aria-current="page">
                                    <div class="flex items-center">
                                        <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                                        <span class="ml-1 text-gray-800 md:ml-2 font-medium">Active Ponds</span>
                                    </div>
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <!-- Filters Bar -->
                    <div class="mb-8 bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div>
                                <label for="farmSelect" class="block text-sm font-medium text-gray-700 mb-2">Farm Location</label>
                                <select id="farmSelect" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>Sikindar Shaik Farm</option>
                                    <option>AquaPrime Holdings</option>
                                    <option>Coastal Fisheries</option>
                                    <option>Oceanic Aquaculture</option>
                                </select>
                            </div>
                            <div>
                                <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                                <input type="date" id="date" value="2025-07-29" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                            </div>
                            <div>
                                <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
                                <select id="statusFilter" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <option>All Statuses</option>
                                    <option>Active</option>
                                    <option>Stocking</option>
                                    <option>Renovation</option>
                                    <option>At Risk</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <button class="w-full bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white font-semibold px-4 py-3 rounded-xl transition duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-filter mr-2"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Summary Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Active Card -->
                        <div class="bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Active Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">8</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>12% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Stocking Card -->
                        <div class="bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Stocking Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">4</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>3% from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- Renovation Card -->
                        <div class="bg-gradient-to-br from-purple-400 to-fuchsia-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Renovation Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">2</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tools text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-equals mr-1"></i>
                                    <span>No change from last month</span>
                                </div>
                            </div>
                        </div>
                        <!-- At Risk Card -->
                        <div class="bg-gradient-to-br from-red-400 to-orange-500 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Ponds At Risk</p>
                                    <p class="text-4xl font-bold text-white mt-1">1</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-down mr-1"></i>
                                    <span>50% from last month</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Main Content - Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Pond Management Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed information on all aquaculture ponds</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search ponds..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-cog mr-1"></i> Settings
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond #</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Shrimp Type</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stocking Date</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Age (Days)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">ABW (g)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Survival %</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Biomass (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Feed/Day (kg)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">FCR</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Row 1 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 1</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-15</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">44</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">28.4 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 89%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">89%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,250 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.2 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.25</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 2 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 2</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-20</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">39</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">26.7 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 92%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">92%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,180 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">42.8 kg</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.22</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Row 3 -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 3</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Stocking
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-07-28</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.0 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">0.8 g</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-gray-300" style="width: 0%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">-</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                                <i class="fas fa-chart-line"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-ellipsis-h"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">5</span> of <span class="font-bold">8</span> results
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-primary-600 to-aqua-600 rounded-xl hover:from-primary-700 hover:to-aqua-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-primary-50 to-aqua-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-primary-100 to-aqua-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-line text-4xl text-primary-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Real-time pond performance analytics</p>
                                    <p class="text-sm text-gray-500 mt-2">Growth rates, feed efficiency, and yield predictions</p>
                                </div>
                            </div>
                        </div>
                        <!-- Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-primary-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-plus text-primary-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Add New Pond</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-aqua-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-sync-alt text-aqua-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Refresh Data</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-file-export text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Export Report</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                                    <div class="flex items-center">
                                        <div class="bg-green-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-bell text-green-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Set Alerts</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Key Performance Indicators -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">89.2%</p>
                                <p class="text-sm text-gray-500">Across active ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.24</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-chart-line text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Growth Rate</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">0.29</p>
                                <p class="text-sm text-gray-500">g/day average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-tachometer-alt text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Efficiency</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">92.5%</p>
                                <p class="text-sm text-gray-500">Overall performance</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tab Content: Farm Report -->
            <div id="farm-report-content" class="tab-content" style="display: none;">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                    <!-- Header Section -->
                    <div class="mb-8">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                            <div>
                                <h1 class="text-4xl font-bold text-gray-900 font-heading">Farm Management Dashboard</h1>
                                <p class="text-gray-700 mt-2">Comprehensive farm operations and production analytics</p>
                            </div>
                            <div class="flex items-center space-x-4">
                                <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                                    <i class="fas fa-calendar-alt mr-2 text-emerald-600"></i>
                                    <span>Season: 2025 Monsoon</span>
                                </div>
                                <button class="bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                                    <i class="fas fa-file-excel mr-2"></i> Export Data
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Overview Cards -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <!-- Total Ponds Card -->
                        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Ponds</p>
                                    <p class="text-4xl font-bold text-white mt-1">15</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-water text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-chart-line mr-1"></i>
                                    <span>Across 3 modules</span>
                                </div>
                            </div>
                        </div>
                        <!-- Total Area Card -->
                        <div class="bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Total Area</p>
                                    <p class="text-4xl font-bold text-white mt-1">45.2</p>
                                    <p class="text-xs text-white opacity-80">Hectares</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-expand-arrows-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>5% expansion planned</span>
                                </div>
                            </div>
                        </div>
                        <!-- Production Capacity Card -->
                        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Production</p>
                                    <p class="text-4xl font-bold text-white mt-1">125</p>
                                    <p class="text-xs text-white opacity-80">Tons/Season</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-fish text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-target mr-1"></i>
                                    <span>Target: 150 tons</span>
                                </div>
                            </div>
                        </div>
                        <!-- Efficiency Card -->
                        <div class="bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl p-6 card-shadow summary-card">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-white">Efficiency</p>
                                    <p class="text-4xl font-bold text-white mt-1">83%</p>
                                </div>
                                <div class="bg-white bg-opacity-20 p-3 rounded-full">
                                    <i class="fas fa-tachometer-alt text-white text-2xl"></i>
                                </div>
                            </div>
                            <div class="mt-4">
                                <div class="flex items-center text-sm text-white">
                                    <i class="fas fa-arrow-up mr-1"></i>
                                    <span>8% from last season</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Farm Production Table -->
                    <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100 mb-8">
                        <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                            <div>
                                <h2 class="text-2xl font-bold text-gray-900 font-heading">Farm Production Overview</h2>
                                <p class="text-gray-600 mt-1">Detailed production data across all farm modules</p>
                            </div>
                            <div class="mt-4 md:mt-0 flex space-x-3">
                                <div class="relative">
                                    <input type="text" placeholder="Search modules..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 transition">
                                    <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                                </div>
                                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                                    <i class="fas fa-filter mr-1"></i> Filter
                                </button>
                            </div>
                        </div>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="table-header">
                                    <tr>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond Count</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Total Area (Ha)</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Season</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stock Density</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">PL Origin</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Expected Yield</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Current Status</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Progress</th>
                                        <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <!-- Module A Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">5 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.2 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Coastal Hatchery</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.6 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 75%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">75%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module B Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">6 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">18.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-blue-100 text-blue-800">
                                                Monsoon 2025
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Premium Aqua</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">52.3 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                                <i class="fas fa-check-circle mr-1"></i> Active
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full" style="width: 68%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">68%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    <!-- Module C Row -->
                                    <tr class="hover-row">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">4 Ponds</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.5 Ha</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full bg-yellow-100 text-yellow-800">
                                                Preparation
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">13.2 pcs/m²</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Marine Genetics</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">38.1 tons</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                                <i class="fas fa-seedling mr-1"></i> Preparing
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                                    <div class="progress-fill h-2 rounded-full bg-yellow-500" style="width: 25%"></div>
                                                </div>
                                                <span class="text-sm text-gray-700">25%</span>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <button class="text-emerald-600 hover:text-emerald-900 mr-3 bg-emerald-50 p-2 rounded-lg">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                            <div class="text-sm text-gray-700 mb-4 md:mb-0">
                                Showing <span class="font-bold">1</span> to <span class="font-bold">3</span> of <span class="font-bold">3</span> modules
                            </div>
                            <div class="flex space-x-2">
                                <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                                    Previous
                                </button>
                                <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-emerald-600 to-teal-600 rounded-xl hover:from-emerald-700 hover:to-teal-700 transition">
                                    Next
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- Additional Analytics Section -->
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                        <!-- Production Analytics -->
                        <div class="bg-gradient-to-br from-emerald-50 to-teal-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Production Analytics</h3>
                            <div class="h-64 bg-gradient-to-r from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center">
                                <div class="text-center">
                                    <i class="fas fa-chart-bar text-4xl text-emerald-500 mb-3 floating"></i>
                                    <p class="text-gray-600">Production analytics chart would appear here</p>
                                    <p class="text-sm text-gray-500 mt-2">Showing yield trends across all modules</p>
                                </div>
                            </div>
                        </div>
                        <!-- Farm Management Tools -->
                        <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Management Tools</h3>
                            <div class="space-y-4">
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-emerald-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-seedling text-emerald-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Plan Stocking</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-teal-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-calendar-check text-teal-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Schedule Harvest</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-blue-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-chart-line text-blue-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">View Reports</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                                <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 rounded-xl transition duration-200 border border-emerald-100">
                                    <div class="flex items-center">
                                        <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                            <i class="fas fa-cog text-purple-600 text-xl"></i>
                                        </div>
                                        <span class="font-bold text-gray-800">Farm Settings</span>
                                    </div>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Metrics -->
                    <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                        <h3 class="text-xl font-bold text-gray-900 mb-6">Key Performance Indicators</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <div class="text-center">
                                <div class="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-fish text-blue-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Survival Rate</h4>
                                <p class="text-3xl font-bold text-blue-600 mt-1">87.5%</p>
                                <p class="text-sm text-gray-500">Average across all ponds</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-emerald-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-weight text-emerald-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Avg. FCR</h4>
                                <p class="text-3xl font-bold text-emerald-600 mt-1">1.28</p>
                                <p class="text-sm text-gray-500">Feed conversion ratio</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-purple-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-clock text-purple-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Cycle Time</h4>
                                <p class="text-3xl font-bold text-purple-600 mt-1">105</p>
                                <p class="text-sm text-gray-500">Days average</p>
                            </div>
                            <div class="text-center">
                                <div class="bg-orange-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-3">
                                    <i class="fas fa-dollar-sign text-orange-600 text-2xl"></i>
                                </div>
                                <h4 class="text-lg font-bold text-gray-900">Profit Margin</h4>
                                <p class="text-3xl font-bold text-orange-600 mt-1">24.3%</p>
                                <p class="text-sm text-gray-500">This season</p>
                            </div>
                        </div>
                    </div>
            </div>
        </div>
    </div>

    <script>
        function setActive(element) {
            const navItems = document.querySelectorAll('.nav-item');
            navItems.forEach(item => item.classList.remove('active'));
            element.classList.add('active');
        }

        function toggleSidebar() {
            const sidebar = document.querySelector('.tomota-sidebar');
            const mainContent = document.querySelector('.main-content');
            const header = document.querySelector('.header-section');

            sidebar.classList.toggle('hidden');
            mainContent.classList.toggle('sidebar-hidden');
            header.classList.toggle('sidebar-hidden');
        }

        function switchTab(tabName, element) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Remove active class from all tabs
            const tabs = document.querySelectorAll('.content-tab');
            tabs.forEach(tab => {
                tab.classList.remove('active');
            });

            // Show selected tab content
            const selectedContent = document.getElementById(tabName + '-content');
            if (selectedContent) {
                selectedContent.style.display = 'block';
            }

            // Add active class to clicked tab
            element.classList.add('active');
        }

        // User dropdown functionality
        function toggleUserDropdown() {
            const dropdown = document.getElementById('userDropdown');
            dropdown.classList.toggle('show');
        }

        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                // Add logout functionality here
                alert('Logging out...');
                // You can redirect to login page or clear session
                // window.location.href = '/login';
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userDropdown');
            const avatarButton = document.querySelector('.avatar-button');

            if (!avatarButton.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.remove('show');
            }
        });

        // Dashboard functionality
        function showDashboard() {
            // Hide all tab content sections
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.style.display = 'none';
            });

            // Hide tabs section
            const tabsSection = document.querySelector('.content-tabs');
            if (tabsSection) {
                tabsSection.style.display = 'none';
            }

            // Show dashboard content
            const dashboardContent = document.getElementById('dashboard-content');
            if (dashboardContent) {
                dashboardContent.style.display = 'block';
            }
        }

        // Function to show tabs (for other nav items)
        function showTabs() {
            // Hide dashboard content
            const dashboardContent = document.getElementById('dashboard-content');
            if (dashboardContent) {
                dashboardContent.style.display = 'none';
            }

            // Show tabs section
            const tabsSection = document.querySelector('.content-tabs');
            if (tabsSection) {
                tabsSection.style.display = 'flex';
            }

            // Show active pond content by default
            document.getElementById('active-pond-content').style.display = 'block';
        }

        // Map functionality
        function switchMapView(viewType) {
            // Update active button
            const buttons = document.querySelectorAll('.map-control-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Simulate map view change
            console.log('Switching to ' + viewType + ' view');
            // Here you would integrate with actual map API (Google Maps, Mapbox, etc.)
        }

        function loadInteractiveMap() {
            const mapPlaceholder = document.querySelector('.map-placeholder');
            const loadBtn = document.querySelector('.load-map-btn');

            // Show loading state
            loadBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Loading Map...';
            loadBtn.disabled = true;

            // Simulate map loading
            setTimeout(() => {
                mapPlaceholder.innerHTML = `
                    <div style="width: 100%; height: 100%; background: linear-gradient(135deg, #4CAF50 0%, #2196F3 100%); display: flex; align-items: center; justify-content: center; position: relative;">
                        <div style="text-align: center; color: white;">
                            <i class="fas fa-map-marked-alt text-6xl mb-4"></i>
                            <h3 style="margin: 0; font-size: 24px; font-weight: 600;">Interactive Map Loaded</h3>
                            <p style="margin: 8px 0 0 0; opacity: 0.9;">Sikindar Shaik Farm - Satellite View</p>
                        </div>
                        <!-- Simulated pond markers -->
                        <div style="position: absolute; top: 30%; left: 25%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond A1 - Active" onclick="focusPond('A1')"></div>
                        <div style="position: absolute; top: 45%; left: 35%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond A2 - Active" onclick="focusPond('A2')"></div>
                        <div style="position: absolute; top: 60%; left: 45%; width: 20px; height: 20px; background: #007bff; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond B1 - Stocking" onclick="focusPond('B1')"></div>
                        <div style="position: absolute; top: 35%; left: 55%; width: 20px; height: 20px; background: #6f42c1; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond B2 - Renovation" onclick="focusPond('B2')"></div>
                        <div style="position: absolute; top: 70%; left: 65%; width: 20px; height: 20px; background: #28a745; border-radius: 50%; border: 3px solid white; cursor: pointer;" title="Pond C1 - Active" onclick="focusPond('C1')"></div>
                    </div>
                `;
            }, 2000);
        }

        function focusPond(pondId) {
            // Highlight selected pond in sidebar
            const pondItems = document.querySelectorAll('.pond-item');
            pondItems.forEach(item => item.classList.remove('selected'));

            // Find and highlight the clicked pond
            pondItems.forEach(item => {
                if (item.textContent.includes(pondId)) {
                    item.classList.add('selected');
                    item.style.background = '#e3f2fd';
                    item.style.borderColor = '#2196f3';
                }
            });

            // Show pond details (you can expand this)
            console.log('Focusing on pond: ' + pondId);

            // You could show a popup with pond details here
            alert('Pond ' + pondId + ' selected. Detailed information would be displayed here.');
        }

        // Satellite Map Functions
        function switchView(viewType) {
            const buttons = document.querySelectorAll('.view-btn');
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            const mapView = document.querySelector('.satellite-map-view');

            if (viewType === 'satellite') {
                // Switch to satellite view
                mapView.style.filter = 'contrast(1.1) saturate(1.2)';
                console.log('Switched to satellite view');
            } else {
                // Switch to map view
                mapView.style.filter = 'contrast(0.9) saturate(0.8) hue-rotate(20deg)';
                console.log('Switched to map view');
            }
        }

        function toggleFullscreen() {
            const mapContainer = document.querySelector('.satellite-map-container');
            const fullscreenBtn = document.querySelector('.fullscreen-btn');

            if (!document.fullscreenElement) {
                mapContainer.requestFullscreen().then(() => {
                    fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                    mapContainer.style.borderRadius = '0';
                }).catch(err => {
                    console.log('Error attempting to enable fullscreen:', err);
                });
            } else {
                document.exitFullscreen().then(() => {
                    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                    mapContainer.style.borderRadius = '12px';
                });
            }
        }

        function showPondInfo(pondId) {
            const pondData = {
                'A1': { name: 'Pond A1', status: 'Active', days: 44, survival: '89%', color: '#10b981' },
                'A2': { name: 'Pond A2', status: 'Active', days: 39, survival: '92%', color: '#10b981' },
                'B1': { name: 'Pond B1', status: 'Stocking', days: 1, survival: 'New stock', color: '#3b82f6' },
                'B2': { name: 'Pond B2', status: 'Renovation', days: 0, survival: 'Maintenance', color: '#f59e0b' },
                'C1': { name: 'Pond C1', status: 'Active', days: 80, survival: '75%', color: '#10b981' },
                'D1': { name: 'Pond D1', status: 'Active', days: 52, survival: '88%', color: '#10b981' },
                'E1': { name: 'Pond E1', status: 'Active', days: 28, survival: '94%', color: '#10b981' }
            };

            const pond = pondData[pondId];
            if (pond) {
                // Create info popup
                const popup = document.createElement('div');
                popup.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: white;
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
                    z-index: 1000;
                    min-width: 300px;
                    text-align: center;
                `;

                popup.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; gap: 10px; margin-bottom: 15px;">
                        <div style="width: 20px; height: 20px; background: ${pond.color}; border-radius: 50%; border: 2px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"></div>
                        <h3 style="margin: 0; color: #333;">${pond.name}</h3>
                    </div>
                    <p style="margin: 5px 0; color: #666;"><strong>Status:</strong> ${pond.status}</p>
                    ${pond.days > 0 ? `<p style="margin: 5px 0; color: #666;"><strong>Days:</strong> ${pond.days}</p>` : ''}
                    <p style="margin: 5px 0; color: #666;"><strong>Survival Rate:</strong> ${pond.survival}</p>
                    <button onclick="this.parentElement.remove()" style="margin-top: 15px; padding: 8px 16px; background: #3b82f6; color: white; border: none; border-radius: 6px; cursor: pointer;">Close</button>
                `;

                document.body.appendChild(popup);

                // Remove popup after 5 seconds
                setTimeout(() => {
                    if (popup.parentElement) {
                        popup.remove();
                    }
                }, 5000);
            }
        }

        function zoomIn() {
            const mapView = document.querySelector('.satellite-map-view');
            const currentScale = mapView.style.transform.match(/scale\(([\d.]+)\)/);
            const scale = currentScale ? parseFloat(currentScale[1]) : 1;
            const newScale = Math.min(scale * 1.2, 3);
            mapView.style.transform = `scale(${newScale})`;
            mapView.style.transformOrigin = 'center center';
        }

        function zoomOut() {
            const mapView = document.querySelector('.satellite-map-view');
            const currentScale = mapView.style.transform.match(/scale\(([\d.]+)\)/);
            const scale = currentScale ? parseFloat(currentScale[1]) : 1;
            const newScale = Math.max(scale / 1.2, 0.5);
            mapView.style.transform = `scale(${newScale})`;
            mapView.style.transformOrigin = 'center center';
        }
    </script>
</body>
</html>
