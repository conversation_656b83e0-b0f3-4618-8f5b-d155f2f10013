```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AquaPond Analytics Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        secondary: {
                            50: '#f5f3ff',
                            100: '#ede9fe',
                            200: '#ddd6fe',
                            300: '#c4b5fd',
                            400: '#a78bfa',
                            500: '#8b5cf6',
                            600: '#7c3aed',
                            700: '#6d28d9',
                            800: '#5b21b6',
                            900: '#4c1d95',
                        },
                        aqua: {
                            50: '#f0fdfa',
                            100: '#ccfbf1',
                            200: '#99f6e4',
                            300: '#5eead4',
                            400: '#2dd4bf',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a',
                        }
                    },
                    fontFamily: {
                        sans: ['Inter', 'sans-serif'],
                        heading: ['Poppins', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            min-height: 100vh;
        }
        .header-gradient {
            background: linear-gradient(135deg, #0ea5e9 0%, #0d9488 100%);
        }
        .card-shadow {
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .status-active {
            background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
            color: white;
        }
        .status-stocking {
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 100%);
            color: white;
        }
        .status-renovation {
            background: linear-gradient(135deg, #a78bfa 0%, #8b5cf6 100%);
            color: white;
        }
        .status-risk {
            background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
            color: white;
        }
        .table-header {
            background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
        }
        .hover-row:hover {
            background-color: #f0f9ff;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .progress-bar {
            height: 8px;
            border-radius: 4px;
        }
        .progress-bg {
            background-color: #e2e8f0;
        }
        .progress-fill {
            background: linear-gradient(90deg, #0ea5e9, #0d9488);
        }
        .summary-card {
            transform: translateY(0);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .summary-card:hover {
            transform: translateY(-5px);
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0.4); }
            70% { box-shadow: 0 0 0 10px rgba(14, 165, 233, 0); }
            100% { box-shadow: 0 0 0 0 rgba(14, 165, 233, 0); }
        }
        .floating {
            animation: floating 3s ease-in-out infinite;
        }
        @keyframes floating {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-cyan-100">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header Section -->
        <div class="mb-8">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
                <div>
                    <h1 class="text-4xl font-bold text-gray-900 font-heading">AquaPond Analytics</h1>
                    <p class="text-gray-700 mt-2">Real-time monitoring and management of aquaculture ponds</p>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center text-sm text-gray-700 bg-white px-4 py-2 rounded-xl card-shadow">
                        <i class="fas fa-sync-alt mr-2 text-primary-600"></i>
                        <span>Last updated: July 29, 2025, 1:54 PM IST</span>
                    </div>
                    <button class="bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white px-5 py-3 rounded-xl font-medium transition duration-300 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-download mr-2"></i> Export Report
                    </button>
                </div>
            </div>
            <!-- Breadcrumbs -->
            <nav class="flex text-sm mb-6" aria-label="Breadcrumb">
                <ol class="inline-flex items-center space-x-1 md:space-x-3">
                    <li class="inline-flex items-center">
                        <a href="#" class="inline-flex items-center text-gray-600 hover:text-primary-600">
                            <i class="fas fa-home mr-2"></i>
                            Home
                        </a>
                    </li>
                    <li>
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                            <a href="#" class="ml-1 text-gray-600 hover:text-primary-600 md:ml-2">Reports</a>
                        </div>
                    </li>
                    <li aria-current="page">
                        <div class="flex items-center">
                            <i class="fas fa-chevron-right text-gray-400 text-xs mx-2"></i>
                            <span class="ml-1 text-gray-800 md:ml-2 font-medium">Active Ponds</span>
                        </div>
                    </li>
                </ol>
            </nav>
        </div>
        <!-- Filters Bar -->
        <div class="mb-8 bg-white rounded-2xl card-shadow p-6 border border-gray-100">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                    <label for="farmSelect" class="block text-sm font-medium text-gray-700 mb-2">Farm Location</label>
                    <select id="farmSelect" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                        <option>Sikindar Shaik Farm</option>
                        <option>AquaPrime Holdings</option>
                        <option>Coastal Fisheries</option>
                        <option>Oceanic Aquaculture</option>
                    </select>
                </div>
                <div>
                    <label for="date" class="block text-sm font-medium text-gray-700 mb-2">Date Range</label>
                    <input type="date" id="date" value="2025-07-29" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                </div>
                <div>
                    <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2">Status Filter</label>
                    <select id="statusFilter" class="w-full p-3 border border-gray-300 rounded-xl shadow-sm focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                        <option>All Statuses</option>
                        <option>Active</option>
                        <option>Stocking</option>
                        <option>Renovation</option>
                        <option>At Risk</option>
                    </select>
                </div>
                <div class="flex items-end">
                    <button class="w-full bg-gradient-to-r from-primary-600 to-aqua-600 hover:from-primary-700 hover:to-aqua-700 text-white font-semibold px-4 py-3 rounded-xl transition duration-300 flex items-center justify-center shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                        <i class="fas fa-filter mr-2"></i> Apply Filters
                    </button>
                </div>
            </div>
        </div>
        <!-- Summary Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Active Card -->
            <div class="bg-gradient-to-br from-green-400 to-emerald-500 rounded-2xl p-6 card-shadow summary-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white">Active Ponds</p>
                        <p class="text-4xl font-bold text-white mt-1">8</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-water text-white text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-white">
                        <i class="fas fa-arrow-up mr-1"></i>
                        <span>12% from last month</span>
                    </div>
                </div>
            </div>
            <!-- Stocking Card -->
            <div class="bg-gradient-to-br from-blue-400 to-indigo-500 rounded-2xl p-6 card-shadow summary-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white">Stocking Ponds</p>
                        <p class="text-4xl font-bold text-white mt-1">4</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-fish text-white text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-white">
                        <i class="fas fa-arrow-down mr-1"></i>
                        <span>3% from last month</span>
                    </div>
                </div>
            </div>
            <!-- Renovation Card -->
            <div class="bg-gradient-to-br from-purple-400 to-fuchsia-500 rounded-2xl p-6 card-shadow summary-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white">Renovation Ponds</p>
                        <p class="text-4xl font-bold text-white mt-1">2</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-tools text-white text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-white">
                        <i class="fas fa-equals mr-1"></i>
                        <span>No change from last month</span>
                    </div>
                </div>
            </div>
            <!-- At Risk Card -->
            <div class="bg-gradient-to-br from-red-400 to-orange-500 rounded-2xl p-6 card-shadow summary-card">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white">Ponds At Risk</p>
                        <p class="text-4xl font-bold text-white mt-1">1</p>
                    </div>
                    <div class="bg-white bg-opacity-20 p-3 rounded-full">
                        <i class="fas fa-exclamation-triangle text-white text-2xl"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex items-center text-sm text-white">
                        <i class="fas fa-arrow-down mr-1"></i>
                        <span>50% from last month</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Main Content - Table -->
        <div class="bg-white rounded-2xl card-shadow overflow-hidden border border-gray-100">
            <div class="px-6 py-5 border-b border-gray-200 flex flex-col md:flex-row md:items-center md:justify-between">
                <div>
                    <h2 class="text-2xl font-bold text-gray-900 font-heading">Pond Management Overview</h2>
                    <p class="text-gray-600 mt-1">Detailed information on all aquaculture ponds</p>
                </div>
                <div class="mt-4 md:mt-0 flex space-x-3">
                    <div class="relative">
                        <input type="text" placeholder="Search ponds..." class="pl-10 pr-4 py-2 border border-gray-300 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-xl font-medium transition duration-200">
                        <i class="fas fa-cog mr-1"></i> Settings
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="table-header">
                        <tr>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Module</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Pond #</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Status</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Shrimp Type</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Stocking Date</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Age (Days)</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Density</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">ABW (g)</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Survival %</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Biomass (kg)</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Feed/Day (kg)</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">FCR</th>
                            <th scope="col" class="px-6 py-4 text-left text-xs font-bold text-gray-800 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <!-- Row 1 -->
                        <tr class="hover-row">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 1</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                    <i class="fas fa-check-circle mr-1"></i> Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-15</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">44</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">12.5 pcs/m²</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">28.4 g</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                        <div class="progress-fill h-2 rounded-full" style="width: 89%"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">89%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,250 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">45.2 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.25</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                        <!-- Row 2 -->
                        <tr class="hover-row">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 2</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-active">
                                    <i class="fas fa-check-circle mr-1"></i> Active
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-06-20</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">39</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">11.8 pcs/m²</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">26.7 g</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                        <div class="progress-fill h-2 rounded-full" style="width: 92%"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">92%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1,180 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">42.8 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.22</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                        <!-- Row 3 -->
                        <tr class="hover-row">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module C</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 3</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-stocking">
                                    <i class="fas fa-seedling mr-1"></i> Stocking
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-07-28</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">15.0 pcs/m²</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">0.8 g</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                        <div class="progress-fill h-2 rounded-full bg-gray-300" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">-</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                        <!-- Row 4 -->
                        <tr class="hover-row">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module A</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 4</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-renovation">
                                    <i class="fas fa-tools mr-1"></i> Renovation
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                        <div class="progress-fill h-2 rounded-full bg-gray-300" style="width: 0%"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">-</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">-</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                        <!-- Row 5 -->
                        <tr class="hover-row">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-bold text-gray-900">Module B</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Pond 5</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-3 py-1 inline-flex text-xs leading-5 font-bold rounded-full status-risk">
                                    <i class="fas fa-exclamation-triangle mr-1"></i> At Risk
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">Vannamei</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">2025-05-10</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">80</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">10.2 pcs/m²</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">22.1 g</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="w-24 progress-bg rounded-full h-2 mr-2">
                                        <div class="progress-fill h-2 rounded-full bg-red-500" style="width: 75%"></div>
                                    </div>
                                    <span class="text-sm text-gray-700">75%</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">980 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">38.5 kg</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700">1.42</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <button class="text-primary-600 hover:text-primary-900 mr-3 bg-primary-50 p-2 rounded-lg">
                                    <i class="fas fa-chart-line"></i>
                                </button>
                                <button class="text-gray-600 hover:text-gray-900 bg-gray-100 p-2 rounded-lg">
                                    <i class="fas fa-ellipsis-h"></i>
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <div class="px-6 py-4 border-t border-gray-200 flex flex-col md:flex-row items-center justify-between">
                <div class="text-sm text-gray-700 mb-4 md:mb-0">
                    Showing <span class="font-bold">1</span> to <span class="font-bold">5</span> of <span class="font-bold">8</span> results
                </div>
                <div class="flex space-x-2">
                    <button class="px-4 py-2 text-sm font-bold text-gray-700 bg-gray-100 rounded-xl hover:bg-gray-200 transition">
                        Previous
                    </button>
                    <button class="px-4 py-2 text-sm font-bold text-white bg-gradient-to-r from-primary-600 to-aqua-600 rounded-xl hover:from-primary-700 hover:to-aqua-700 transition">
                        Next
                    </button>
                </div>
            </div>
        </div>
        <!-- Additional Info Section -->
        <div class="mt-8 grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div class="bg-gradient-to-br from-primary-50 to-aqua-50 rounded-2xl card-shadow p-6 lg:col-span-2 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Performance Overview</h3>
                <div class="h-64 bg-gradient-to-r from-primary-100 to-aqua-100 rounded-xl flex items-center justify-center">
                    <div class="text-center">
                        <i class="fas fa-chart-line text-4xl text-primary-500 mb-3 floating"></i>
                        <p class="text-gray-600">Performance chart visualization would appear here</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-2xl card-shadow p-6 border border-gray-100">
                <h3 class="text-xl font-bold text-gray-900 mb-4">Quick Actions</h3>
                <div class="space-y-4">
                    <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                        <div class="flex items-center">
                            <div class="bg-primary-100 p-3 rounded-xl mr-3">
                                <i class="fas fa-plus text-primary-600 text-xl"></i>
                            </div>
                            <span class="font-bold text-gray-800">Add New Pond</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                        <div class="flex items-center">
                            <div class="bg-aqua-100 p-3 rounded-xl mr-3">
                                <i class="fas fa-sync-alt text-aqua-600 text-xl"></i>
                            </div>
                            <span class="font-bold text-gray-800">Refresh Data</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    <button class="w-full flex items-center justify-between p-4 bg-gradient-to-r from-blue-50 to-cyan-50 hover:from-blue-100 hover:to-cyan-100 rounded-xl transition duration-200 border border-blue-100">
                        <div class="flex items-center">
                            <div class="bg-purple-100 p-3 rounded-xl mr-3">
                                <i class="fas fa-file-export text-purple-600 text-xl"></i>
                            </div>
                            <span class="font-bold text-gray-800">Export Report</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
```